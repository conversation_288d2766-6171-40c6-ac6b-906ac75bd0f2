// Thin browser-safe stub for `@prisma/client` so that legacy service files still compile
// in the frontend bundle. All methods throw at runtime; services should migrate to REST
// calls instead of using a DB client in the browser.
// This file will be deleted once we finish removing all Prisma-based stubs.

export class PrismaClient {
	constructor() {
		if (typeof window !== 'undefined') {
			// eslint-disable-next-line no-console
			console.warn('⚠️  @prisma/client stub: PrismaClient should not be instantiated in the browser');
		}
	}

	// Return Proxy that throws for any property access
	private static _throw(): never {
		throw new Error('PrismaClient stub invoked in browser – migrate this code path to use REST API');
	}

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	public get $(name: string): never {
		return PrismaClient._throw();
	}
}

export type Prisma = unknown;
