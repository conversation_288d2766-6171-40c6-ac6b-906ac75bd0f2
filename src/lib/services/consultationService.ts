import { consultationsApi, type UpdateConsultationData } from '@/api/consultations';
import { apiClient, ApiResponse } from '@/api/client';
import { type Consultation } from '../../api/consultations';

// Thin client-side wrapper: delegates to backend REST API. No direct DB access.

export const consultationService = {
  // Get consultation by ID
  async getConsultationById(consultationId: string): Promise<ApiResponse<Consultation>> {
    try {
      return consultationsApi.getConsultation(consultationId);
    } catch (error) {
      console.error('Error fetching consultation:', error);
      return {
        success: false,
        message: 'Failed to fetch consultation',
      };
    }
  },

  // Get consultation by appointment ID
  async getConsultationByAppointmentId(appointmentId: string): Promise<ApiResponse<Consultation>> {
    try {
      return consultationsApi.getConsultationByAppointment(appointmentId);
    } catch (error) {
      console.error('Error fetching consultation by appointment ID:', error);
      return {
        success: false,
        message: 'Failed to fetch consultation',
      };
    }
  },

  // Create a consultation room
  async createConsultationRoom(appointmentId: string): Promise<ApiResponse<Consultation>> {
    try {
      // Backend endpoint handles room creation
      return apiClient.post('/consultations/room', { appointmentId });
    } catch (error) {
      console.error('Error creating consultation room:', error);
      return {
        success: false,
        message: 'Failed to create consultation room',
      };
    }
  },

  // Generate a room token for a consultation
  async generateRoomToken(consultationId: string, userId: string): Promise<ApiResponse<{ token: string }>> {
    try {
      return consultationsApi.generateToken(consultationId, { userId });
    } catch (error) {
      console.error('Error generating room token:', error);
      return {
        success: false,
        message: 'Failed to generate room token',
      };
    }
  },

  // Create a new consultation
  async createConsultation(appointmentId: string): Promise<ApiResponse<Consultation>> {
    try {
      return consultationsApi.createConsultation({ appointmentId });
    } catch (error) {
      console.error('Error creating consultation:', error);
      return {
        success: false,
        message: 'Failed to create consultation',
      };
    }
  },

  // Update consultation status
  async updateConsultationStatus(consultationId: string, status: Consultation['status']): Promise<ApiResponse<Consultation>> {
    try {
      return consultationsApi.updateConsultation(consultationId, { status });
    } catch (error) {
      console.error('Error updating consultation status:', error);
      return {
        success: false,
        message: 'Failed to update consultation status',
      };
    }
  },

  // Update consultation
  async updateConsultation(consultationId: string, data: UpdateConsultationData): Promise<ApiResponse<Consultation>> {
    try {
      return consultationsApi.updateConsultation(consultationId, data);
    } catch (error) {
      console.error('Error updating consultation:', error);
      return {
        success: false,
        message: 'Failed to update consultation',
      };
    }
  },
};

