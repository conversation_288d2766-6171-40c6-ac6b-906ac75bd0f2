import {
	appointmentsApi,
	Appointment,
	CreateAppointmentData,
	UpdateAppointmentData,
	JoinConsultationResponse,
} from '@/api/appointments';
import { ApiResponse } from '@/api/client';

// Compatibility types for hooks/components that currently import from appointmentService
export type AppointmentCreateInput = CreateAppointmentData;
export type AppointmentUpdateInput = UpdateAppointmentData;

// Utility to reshape ApiResponse into legacy `{ success, <key>?, message? }` object
const mapApi = <T, K extends string>(
	resp: ApiResponse<T>,
	key: K,
): { success: true } & Record<K, T> | { success: false; message: string } => {
	return resp.success
		? ({ success: true, [key]: resp.data! } as { success: true } & Record<K, T>)
		: { success: false, message: resp.error ?? resp.message ?? 'Request failed' };
};

export const appointmentService = {
	// ---------- Queries ----------
	async getAll(): Promise<Appointment[]> {
		const r = await appointmentsApi.getAppointments();
		return r.success ? r.data!.appointments : [];
	},

	async getUpcoming(): Promise<Appointment[]> {
		const r = await appointmentsApi.getAppointments({ dateFrom: new Date().toISOString() });
		return r.success ? r.data!.appointments : [];
	},

	async getById(id: string): Promise<{ success: true; appointment: Appointment } | { success: false; message: string }> {
		return mapApi(await appointmentsApi.getAppointment(id), 'appointment');
	},

	// ---------- Mutations ----------
	async create(data: AppointmentCreateInput) {
		return mapApi(await appointmentsApi.createAppointment(data), 'appointment');
	},

	// alias expected by BookingFlow
	createAppointment(data: AppointmentCreateInput) {
		return this.create(data);
	},

	async update(id: string, data: AppointmentUpdateInput) {
		return mapApi(await appointmentsApi.updateAppointment(id, data), 'appointment');
	},

	async delete(id: string) {
		return mapApi(await appointmentsApi.cancelAppointment(id), 'appointment');
	},

	// ---------- Actions ----------
	async joinConsultation(appointmentId: string) {
		return mapApi(await appointmentsApi.joinConsultation(appointmentId), 'consultation');
	},
};
