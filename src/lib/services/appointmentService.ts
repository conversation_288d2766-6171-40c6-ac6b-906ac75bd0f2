import {
	appointmentsApi,
	Appointment,
	CreateAppointmentData,
	UpdateAppointmentData,
	JoinConsultationResponse,
} from '@/api/appointments';
import { consultationsApi } from '@/api/consultations';
import { ApiResponse } from '@/api/client';
import { consultationService } from './consultationService';
import { notificationService } from './notificationService';

// Browser-side service that wraps the typed REST helpers. No direct DB access.

export interface CreateAppointmentParams {
  providerId: string;
  patientId: string;
  appointmentDate: Date;
  reason?: string;
  consultationType: 'VIDEO' | 'AUDIO';
}

// Helper to normalise ApiResponse into the legacy shape { success, message?, <datakey> }
const _map = <T, K extends string>(
	resp: ApiResponse<T>,
	key: K,
): { success: true } & Record<K, T> | { success: false; message: string } => {
	return resp.success
		? { success: true, [key]: resp.data! } as any
		: { success: false, message: resp.error ?? resp.message ?? 'Request failed' };
};

export const appointmentService = {
  // Get appointments for a user
  	// Legacy wrapper aliases ----------

	async getAll(): Promise<Appointment[]> {
		const resp = await appointmentsApi.getAppointments();
		return resp.success ? resp.data!.appointments : [];
	},

	async getUpcoming(): Promise<Appointment[]> {
		const resp = await appointmentsApi.getAppointments({ dateFrom: new Date().toISOString() });
		return resp.success ? resp.data!.appointments : [];
	},

	async getUserAppointments(userId: string, role: string): Promise<Appointment[]> {
		const filter: any = role === 'PROVIDER' ? { providerId: userId } : { patientId: userId };
		const resp = await appointmentsApi.getAppointments(filter);
		return resp.success ? resp.data!.appointments : [];
	},
    try {
      // removed DB where
        ? { provider: { userId } }
        : { patient: { userId } };

      const appointments = await prisma.appointment.findMany({
        where,
        include: {
          provider: {
            include: {
              user: true,
            },
          },
          patient: {
            include: {
              user: true,
            },
          },
          consultation: true,
        },
        orderBy: {
          appointmentDate: 'asc',
        },
      });

      return appointments;
    } catch (error) {
      console.error('Error fetching appointments:', error);
      return [];
    }
  },

  // Get appointment by ID
  	async getAppointmentById(appointmentId: string): Promise<{ success: true; appointment: Appointment } | { success: false; message: string }> {
    try {
      		const resp = await appointmentsApi.getAppointment(appointmentId);
		return _map(resp, 'appointment');
        where: { id: appointmentId },
        include: {
          provider: {
            include: {
              user: true,
            },
          },
          patient: {
            include: {
              user: true,
            },
          },
          consultation: true,
        },
      });

      if (!appointment) {
        return {
          success: false,
          message: 'Appointment not found',
        };
      }

      return {
        success: true,
        appointment,
      };
    } catch (error) {
      console.error('Error fetching appointment:', error);
      return {
        success: false,
        message: 'Failed to fetch appointment',
      };
    }
  },

  // Create a new appointment
  	async createAppointment(params: CreateAppointmentParams): Promise<{ success: true; appointment: Appointment } | { success: false; message: string }> {
    try {
      // Create appointment
      		const data: CreateAppointmentData = {
			patientId: params.patientId,
			providerId: params.providerId,
			consultationType: params.consultationType,
			reason: params.reason,
			scheduledAt: params.appointmentDate.toISOString(),
		};

		const resp = await appointmentsApi.createAppointment(data);
		return _map(resp, 'appointment');
        data: {
          providerId: params.providerId,
          patientId: params.patientId,
          appointmentDate: params.appointmentDate,
          reason: params.reason || '',
          status: 'SCHEDULED',
          consultationType: params.consultationType,
        },
      });

      // Send notifications
      // notification handled server-side now

      return {
        success: true,
        appointment,
      };
    } catch (error) {
      console.error('Error creating appointment:', error);
      return {
        success: false,
        message: 'Failed to create appointment',
      };
    }
  },

  // Update appointment status
  	async updateAppointmentStatus(appointmentId: string, status: Appointment['status']): Promise<{ success: true; appointment: Appointment } | { success: false; message: string }> {
    try {
      const appointment = await prisma.appointment.update({
        where: { id: appointmentId },
        data: {
          status,
          updatedAt: new Date(),
        },
      });

      return {
        success: true,
        appointment,
      };
    } catch (error) {
      console.error('Error updating appointment status:', error);
      return {
        success: false,
        message: 'Failed to update appointment status',
      };
    }
  },

  // Cancel appointment
  	async cancelAppointment(appointmentId: string): Promise<{ success: true; appointment: Appointment } | { success: false; message: string }> {
    try {
      const appointment = await prisma.appointment.update({
        where: { id: appointmentId },
        data: {
          status: 'CANCELLED',
          updatedAt: new Date(),
        },
      });

      // TODO: Send cancellation notifications

      return {
        success: true,
        appointment,
      };
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      return {
        success: false,
        message: 'Failed to cancel appointment',
      };
    }
  },

  // Join consultation
  	async joinConsultation(appointmentId: string): Promise<{ success: true; consultation: JoinConsultationResponse } | { success: false; message: string }> {
    try {
      // Get appointment
      const appointmentResult = await this.getAppointmentById(appointmentId);
      if (!appointmentResult.success) {
        return appointmentResult;
      }

      const appointment = appointmentResult.appointment;

      // Check if appointment is scheduled
      if (appointment.status !== 'SCHEDULED') {
        return {
          success: false,
          message: `Cannot join consultation. Appointment status is ${appointment.status}`,
        };
      }

      // Create or get consultation
      let consultation;
      if (appointment.consultation) {
        consultation = appointment.consultation;
      } else {
        const consultationResult = await consultationService.createConsultation(appointmentId);
        if (!consultationResult.success) {
          return consultationResult;
        }
        consultation = consultationResult.consultation;
      }

      // Update appointment status to IN_PROGRESS
      await this.updateAppointmentStatus(appointmentId, 'IN_PROGRESS');

      return {
        success: true,
        consultation,
      };
    } catch (error) {
      console.error('Error joining consultation:', error);
      return {
        success: false,
        message: 'Failed to join consultation',
      };
    }
  },
};

