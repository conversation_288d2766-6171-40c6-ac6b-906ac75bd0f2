import DailyIframe, {
  DailyCall,
  DailyEventObjectAppMessage,
  DailyEventObjectFatalError,
  DailyParticipant,
  DailyEventObjectParticipants,
  DailyEventObjectNetworkQuality,
} from '@daily-co/daily-js';

type ParticipantState = {
  video: boolean;
  audio: boolean;
  screen: boolean;
  networkQuality?: number;
};

type ParticipantUpdateCallback = (
  participants: Record<string, ParticipantState>
) => void;
type ErrorCallback = (error: DailyEventObjectFatalError) => void;

class DailyService {
  private callObject: DailyCall | null = null;
  private participants: Record<string, ParticipantState> = {};
  private onParticipantUpdate: ParticipantUpdateCallback | null = null;
  private onError: ErrorCallback | null = null;
  public isAudioEnabled: boolean = true;
  public isVideoEnabled: boolean = true;
  public isScreenSharing: boolean = false;
  private onVideoRequestCb: (() => void) | null = null;
  private onVideoRequestResponseCb: ((accepted: boolean) => void) | null = null;

  private updateParticipant(participant: DailyParticipant) {
    this.participants[participant.session_id] = {
      video: Boolean(participant.video),
      audio: Boolean(participant.audio),
      screen: Boolean(participant.screen),
    };
    if (this.onParticipantUpdate) {
      this.onParticipantUpdate({ ...this.participants });
    }
  }

  private handleParticipantJoined = (event?: {
    participant: DailyParticipant;
  }) => {
    if (event?.participant) {
      this.updateParticipant(event.participant);
    }
  };

  private handleParticipantLeft = (event?: {
    participant: DailyParticipant;
  }) => {
    if (event?.participant) {
      delete this.participants[event.participant.session_id];
      if (this.onParticipantUpdate) {
        this.onParticipantUpdate({ ...this.participants });
      }
    }
  };

  private handleParticipantUpdated = (event?: {
    participant: DailyParticipant;
  }) => {
    if (event?.participant) {
      this.updateParticipant(event.participant);
    }
  };
  
  private handleError = (event?: DailyEventObjectFatalError) => {
    if (event && this.onError) {
        this.onError(event);
    }
  };

  private handleLeftMeeting = () => {
    this.reset();
  };

  private handleAppMessage = (event?: DailyEventObjectAppMessage) => {
    if (!event?.data) return;
    const { data } = event;
    if (data.type === 'video-request' && this.onVideoRequestCb) {
      this.onVideoRequestCb();
    }
    if (data.type === 'video-request-response' && this.onVideoRequestResponseCb) {
      this.onVideoRequestResponseCb(Boolean(data.accepted));
    }
  };
  
  private handleNetworkQuality = (event?: DailyEventObjectNetworkQuality) => {
    if (event?.threshold) {
      Object.entries(this.participants).forEach(([id, participant]) => {
        if (event.participants[id]) {
          participant.networkQuality = event.threshold;
        }
      });
      if (this.onParticipantUpdate) {
        this.onParticipantUpdate({ ...this.participants });
      }
    }
  };
  
  private setupEventListeners() {
    if (!this.callObject) return;

    this.callObject.on('participant-joined', this.handleParticipantJoined);
    this.callObject.on('participant-updated', this.handleParticipantUpdated);
    this.callObject.on('participant-left', this.handleParticipantLeft);
    this.callObject.on('error', this.handleError);
    this.callObject.on('left-meeting', this.handleLeftMeeting);
    this.callObject.on('app-message', this.handleAppMessage);
    this.callObject.on('network-quality', this.handleNetworkQuality);
  }

  private removeEventListeners() {
    if (!this.callObject) return;
    
    this.callObject.off('participant-joined', this.handleParticipantJoined);
    this.callObject.off('participant-updated', this.handleParticipantUpdated);
    this.callObject.off('participant-left', this.handleParticipantLeft);
    this.callObject.off('error', this.handleError);
    this.callObject.off('left-meeting', this.handleLeftMeeting);
    this.callObject.off('app-message', this.handleAppMessage);
    this.callObject.off('network-quality', this.handleNetworkQuality);
  }

  async initializeCall(roomUrl: string, token?: string, options?: { 
    video?: boolean, 
    audio?: boolean,
    enableRecording?: boolean,
    enableWaitingRoom?: boolean
  }): Promise<boolean> {
    if (this.callObject) {
      await this.endCall();
    }
    
    this.callObject = DailyIframe.createCallObject({
      url: roomUrl,
      token: token,
      dailyConfig: {
        useDevicePreferenceCookies: true,
        bandwidth: { send: 3000, recv: 3000 } // Higher quality for therapy
      }
    });

    this.setupEventListeners();

    try {
      await this.callObject.preload();
      
      const joinConfig = {
        url: roomUrl,
        token,
        startVideoOff: !(options?.video ?? true),
        startAudioOff: !(options?.audio ?? true),
        showLeaveButton: true,
        properties: {
          enable_waiting_room: options?.enableWaitingRoom ?? false
        }
      };

      await this.callObject.join(joinConfig);

      if (options?.enableRecording) {
        await this.callObject.startRecording({
          cloud: true,
          format: 'mp4'
        });
      }

      // Track initial state
      this.isVideoEnabled = options?.video ?? true;
      this.isAudioEnabled = options?.audio ?? true;

      return true;
    } catch (err) {
      console.error('Error initializing therapy session', err);
      this.reset();
      return false;
    }
  }

  onParticipantsChange(callback: ParticipantUpdateCallback): void {
    this.onParticipantUpdate = callback;
  }
  
  onErrorOccurred(callback: ErrorCallback): void {
    this.onError = callback;
  }
  
  async toggleAudio(): Promise<boolean> {
    if (!this.callObject) return false;
    try {
      await this.callObject.setLocalAudio(!this.isAudioEnabled);
      this.isAudioEnabled = !this.isAudioEnabled;
      return true;
    } catch (err) {
      console.error('Error toggling audio', err);
      return false;
    }
  }

  async toggleVideo(): Promise<boolean> {
    if (!this.callObject) return false;
    try {
      await this.callObject.setLocalVideo(!this.isVideoEnabled);
      this.isVideoEnabled = !this.isVideoEnabled;
      return true;
    } catch (err) {
      console.error('Error toggling video', err);
      return false;
    }
  }

  async enableVideo(): Promise<boolean> {
    if (!this.callObject) return false;
    try {
      await this.callObject.setLocalVideo(true);
      this.isVideoEnabled = true;
      return true;
    } catch (err) {
      console.error('Error enabling video', err);
      return false;
    }
  }

  async shareScreen(): Promise<boolean> {
    if (!this.callObject) return false;
    try {
      const { local } = this.callObject.participants();
      const isCurrentlySharing = Boolean(local?.screen);
      if (isCurrentlySharing) {
        await this.callObject.stopScreenShare();
      } else {
        await this.callObject.startScreenShare();
      }
      this.isScreenSharing = !isCurrentlySharing;
      return true;
    } catch (err) {
      console.error('Error toggling screen share', err);
      return false;
    }
  }

  // Backwards compatibility alias
  async toggleScreenShare(): Promise<boolean> {
    return this.shareScreen();
  }

  async sendVideoRequest(): Promise<boolean> {
    if (!this.callObject) return false;
    try {
      await this.callObject.sendAppMessage({ type: 'video-request' }, '*');
      return true;
    } catch (err) {
      console.error('Error sending video request', err);
      return false;
    }
  }

  async sendVideoRequestResponse(accepted: boolean): Promise<boolean> {
    if (!this.callObject) return false;
    try {
      await this.callObject.sendAppMessage({ type: 'video-request-response', accepted }, '*');
      return true;
    } catch (err) {
      console.error('Error sending video request response', err);
      return false;
    }
  }

  onVideoRequestReceived(callback: () => void): void {
    this.onVideoRequestCb = callback;
  }

  onVideoRequestResponseReceived(callback: (accepted: boolean) => void): void {
    this.onVideoRequestResponseCb = callback;
  }
  
  private reset() {
    this.removeEventListeners();
    this.callObject = null;
    this.participants = {};
    if (this.onParticipantUpdate) {
      this.onParticipantUpdate({});
    }
  }

  async endCall(): Promise<void> {
    if (this.callObject) {
      try {
        await this.callObject.leave();
        await this.callObject.destroy();
      } catch (err) {
        console.error('Error ending call', err);
      } finally {
        this.reset();
      }
    }
  }
  
  destroyCall(): void {
    if (this.callObject) {
        this.callObject.destroy();
        this.reset();
    }
  }

  getCallObject(): DailyCall | null {
    return this.callObject;
  }
  
  getParticipants(): Record<string, ParticipantState> {
    return { ...this.participants };
  }

  async subscribeToTracks(trackTypes: ('video' | 'audio' | 'screen')[]) {
    if (!this.callObject) return false;
    try {
      await this.callObject.updateParticipant({
        setSubscribedTracks: {
          tracks: trackTypes,
        },
      });
      return true;
    } catch (err) {
      console.error('Error subscribing to tracks', err);
      return false;
    }
  }

  // Simplified for 1:1 sessions
  async toggleRecording(): Promise<boolean> {
    if (!this.callObject) return false;
    try {
      const recordingState = await this.callObject.getRecordingState();
      if (recordingState?.isRecording) {
        await this.callObject.stopRecording();
      } else {
        await this.callObject.startRecording({ cloud: true, format: 'mp4' });
      }
      return true;
    } catch (err) {
      console.error('Error toggling recording', err);
      return false;
    }
  }
}

export const dailyService = new DailyService();

