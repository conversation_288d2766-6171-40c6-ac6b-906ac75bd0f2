import { calendarApi, WeeklyAvailability, TimeSlot as ApiTimeSlot } from '@/api/calendar';
import { ApiResponse } from '@/api/client';

export interface TimeSlot {
  startTime: Date;
  endTime: Date;
  isAvailable: boolean;
}

export interface AvailabilityResult {
  success: boolean;
  timeSlots?: TimeSlot[];
  message?: string;
}

export interface WeeklyAvailabilityDay {
  date: Date;
  dayOfWeek: number;
  isAvailable: boolean;
  startTime: string | null;
  endTime: string | null;
}

// Utility to reshape API responses and handle data transformation
const mapApiTimeSlots = (apiSlots: ApiTimeSlot[]): TimeSlot[] => {
  return apiSlots.map(slot => ({
    startTime: new Date(slot.startTime),
    endTime: new Date(slot.endTime),
    isAvailable: slot.isAvailable,
  }));
};

export const calendarService = {
  async getAvailableTimeSlots(providerId: string, date: Date): Promise<AvailabilityResult> {
    const dateString = date.toISOString().split('T')[0];
    const response = await calendarApi.getDayAvailability(providerId, dateString);

    if (response.success && response.data) {
      return {
        success: true,
        timeSlots: mapApiTimeSlots(response.data.timeSlots),
      };
    }
    return { success: false, message: response.error || 'Failed to get time slots' };
  },

  async getProviderWeeklyAvailability(providerId: string, startDate: Date): Promise<WeeklyAvailabilityDay[]> {
    const response = await calendarApi.getProviderWeeklyAvailability(providerId, startDate);
    if (response.success && response.data) {
      return response.data.map(day => ({
        ...day,
        date: new Date(day.date),
      }));
    }
    return [];
  },
};
