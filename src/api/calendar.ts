import { apiClient, ApiResponse } from './client';

// Calendar types
export interface AvailabilityRequest {
  providerId: string;
  dateFrom: Date;
  dateTo: Date;
  consultationType?: 'VIDEO' | 'AUDIO';
}

export interface TimeSlot {
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  isBlocked?: boolean;
  blockReason?: string;
  appointmentId?: string;
}

export type WeeklyAvailability = {
  date: string; // ISO string
  dayOfWeek: number;
  isAvailable: boolean;
  startTime: string | null;
  endTime: string | null;
}[];

export interface DayAvailability {
  date: string;
  isAvailable: boolean;
  timeSlots: TimeSlot[];
  workingHours: {
    start: string;
    end: string;
  };
}

export interface CalendarEvent {
  id: string;
  providerId: string;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  type: 'appointment' | 'blocked' | 'break' | 'personal';
  appointmentId?: string;
  isRecurring?: boolean;
  recurrenceRule?: string;
  color?: string;
}

export interface ProviderSchedule {
  providerId: string;
  workingHours: {
    [key: string]: {
      isWorking: boolean;
      start: string;
      end: string;
      breaks?: Array<{
        start: string;
        end: string;
        title: string;
      }>;
    };
  };
  timeZone: string;
  slotDuration: number; // in minutes
  bufferTime: number; // in minutes
}

export interface BlockTimeSlotRequest {
  providerId: string;
  date: Date;
  startTime: string;
  endTime: string;
  reason?: string;
}

export interface CalendarExport {
  filename: string;
  icsContent: string;
}

// Calendar API
export const calendarApi = {
  async getProviderWeeklyAvailability(providerId: string, startDate: Date): Promise<ApiResponse<WeeklyAvailability>> {
    const params = { startDate: startDate.toISOString() };
    return apiClient.get(`/calendar/availability/week/${providerId}`, { params });
  },

  async getAvailability(request: AvailabilityRequest): Promise<ApiResponse<DayAvailability[]>> {
    return apiClient.get<DayAvailability[]>('/calendar/availability', {
      params: {
        providerId: request.providerId,
        dateFrom: request.dateFrom.toISOString(),
        dateTo: request.dateTo.toISOString(),
      },
    });
  },

  async getDayAvailability(providerId: string, date: string): Promise<ApiResponse<DayAvailability>> {
    return apiClient.get<DayAvailability>(`/calendar/availability/day/${providerId}`, {
      params: { date },
    });
  },

  async getCalendarEvents(providerId: string, from: Date, to: Date): Promise<ApiResponse<CalendarEvent[]>> {
    return apiClient.get<CalendarEvent[]>('/calendar/events', {
      params: {
        providerId,
        from: from.toISOString(),
        to: to.toISOString(),
      },
    });
  },

  async getProviderSchedule(providerId: string): Promise<ApiResponse<ProviderSchedule>> {
    return apiClient.get<ProviderSchedule>(`/calendar/schedule/${providerId}`);
  },

  async updateProviderSchedule(schedule: ProviderSchedule): Promise<ApiResponse<ProviderSchedule>> {
    return apiClient.put<ProviderSchedule>(`/calendar/schedule/${schedule.providerId}`, schedule);
  },

  async blockTimeSlot(request: BlockTimeSlotRequest): Promise<ApiResponse<CalendarEvent>> {
    return apiClient.post<CalendarEvent>('/calendar/block-slot', request);
  },

  async unblockTimeSlot(eventId: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/calendar/unblock-slot/${eventId}`);
  },

  async exportToICS(providerId: string, from: Date, to: Date): Promise<ApiResponse<CalendarExport>> {
    return apiClient.get<CalendarExport>('/calendar/export/ics', {
      params: {
        providerId,
        from: from.toISOString(),
        to: to.toISOString(),
      },
    });
  },

  async createCalendarEvent(event: Omit<CalendarEvent, 'id'>): Promise<ApiResponse<CalendarEvent>> {
    return apiClient.post<CalendarEvent>('/calendar/events', event);
  },

  async updateCalendarEvent(eventId: string, event: Partial<CalendarEvent>): Promise<ApiResponse<CalendarEvent>> {
    return apiClient.put<CalendarEvent>(`/calendar/events/${eventId}`, event);
  },

  async deleteCalendarEvent(eventId: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/calendar/events/${eventId}`);
  },

  async generateCalendarExport(appointmentId: string): Promise<ApiResponse<CalendarExport>> {
    return apiClient.get<CalendarExport>(`/calendar/export/${appointmentId}`);
  },

  async syncExternalCalendar(providerId: string, calendarType: 'google' | 'outlook' | 'apple'): Promise<ApiResponse<{
    syncStatus: 'success' | 'failed';
    eventsImported: number;
    lastSyncAt: string;
  }>> {
    return apiClient.post(`/calendar/sync/${providerId}`, { calendarType });
  },

  async getCalendarIntegrationStatus(providerId: string): Promise<ApiResponse<{
    google?: { connected: boolean; lastSync?: string };
    outlook?: { connected: boolean; lastSync?: string };
    apple?: { connected: boolean; lastSync?: string };
  }>> {
    return apiClient.get(`/calendar/integrations/${providerId}`);
  },

  async connectExternalCalendar(providerId: string, calendarType: 'google' | 'outlook' | 'apple', authCode: string): Promise<ApiResponse<{
    connected: boolean;
    calendarName: string;
  }>> {
    return apiClient.post(`/calendar/connect/${providerId}`, {
      calendarType,
      authCode
    });
  },

  async disconnectExternalCalendar(providerId: string, calendarType: 'google' | 'outlook' | 'apple'): Promise<ApiResponse<void>> {
    return apiClient.delete(`/calendar/connect/${providerId}/${calendarType}`);
  },
};

export default calendarApi;
