import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import DailyIframe, {
	DailyCall,
	DailyParticipant,
	DailyEvent,
	DailyEventObject,
	DailyEventObjectParticipant,
	DailyEventObjectFatalError
} from '@daily-co/daily-js';
import { dailyService } from '../../lib/services/dailyService';

const mockCallObject = {
	join: vi.fn(),
	leave: vi.fn(),
	destroy: vi.fn(),
	on: vi.fn(),
	off: vi.fn(),
	participants: vi.fn<[], { local: Partial<DailyParticipant> }>(),
	setLocalVideo: vi.fn(),
	setLocalAudio: vi.fn(),
	startScreenShare: vi.fn(),
	stopScreenShare: vi.fn(),
	sendAppMessage: vi.fn(),
	startRecording: vi.fn()
};

// Mock the DailyIframe to return our typed mock object.
vi.mock('@daily-co/daily-js', () => ({
	default: {
		createCallObject: vi.fn(() => mockCallObject as unknown as DailyCall)
	}
}));

describe('dailyService', () => {
	// Store event handlers to simulate events precisely.
	const eventHandlers: { [K in DailyEvent]?: (event: DailyEventObject) => void } = {};

	beforeEach(() => {
		// Reset all mocks and clear stored handlers.
		vi.clearAllMocks();
		for (const key in eventHandlers) {
			delete eventHandlers[key as DailyEvent];
		}

		// Set default mock implementations for all methods.
		mockCallObject.join.mockResolvedValue(undefined);
		mockCallObject.leave.mockResolvedValue(undefined);
		mockCallObject.destroy.mockResolvedValue(undefined);
		mockCallObject.setLocalVideo.mockResolvedValue(undefined);
		mockCallObject.setLocalAudio.mockResolvedValue(undefined);
		mockCallObject.startScreenShare.mockResolvedValue(undefined);
		mockCallObject.stopScreenShare.mockResolvedValue(undefined);
		mockCallObject.sendAppMessage.mockResolvedValue(undefined);
		mockCallObject.startRecording.mockResolvedValue(undefined);
		mockCallObject.participants.mockReturnValue({
			local: {
				session_id: 'local',
				user_name: 'Local User',
				video: true,
				audio: true,
				screen: false,
				local: false,
				owner: false,
				record: false,
				user_id: 'user1',
				tracks: {
					audio: { state: 'playable', subscribed: true },
					video: { state: 'playable', subscribed: true },
					screenAudio: { state: 'loading', subscribed: false },
					screenVideo: { state: 'loading', subscribed: false }
				},
				will_eject_at: new Date(),
				permissions: { canSend: true, hasPresence: true, canAdmin: false },
				cam_info: {},
				screen_info: {}
			}
		});

		// Capture event handlers when 'on' is called.
		mockCallObject.on.mockImplementation((event: DailyEvent, handler: (event: DailyEventObject) => void) => {
			eventHandlers[event] = handler;
		});
	});

	afterEach(async () => {
		await dailyService.endCall();
	});

	describe('initializeCall', () => {
		it('should initialize a call with default options', async () => {
			const roomUrl = 'https://example.daily.co/room123';
			const token = 'test-token';

			const result = await dailyService.initializeCall(roomUrl, token);

			expect(result).toBe(true);
			expect(DailyIframe.createCallObject).toHaveBeenCalledWith({
				url: roomUrl,
				token: token,
				dailyConfig: {
					useDevicePreferenceCookies: true,
					bandwidth: { send: 3000, recv: 3000 }
				}
			});
			expect(mockCallObject.join).toHaveBeenCalledWith({
				url: roomUrl,
				token,
				startVideoOff: false,
				startAudioOff: false,
				showLeaveButton: true,
				properties: {
					enable_waiting_room: false
				}
			});
		});

		it('should initialize a call with recording enabled', async () => {
			const roomUrl = 'https://example.daily.co/room123';
			const token = 'test-token';

			await dailyService.initializeCall(roomUrl, token, { enableRecording: true });

			expect(mockCallObject.startRecording).toHaveBeenCalledWith({ cloud: true, format: 'mp4' });
		});

		it('should return false on initialization error', async () => {
			const roomUrl = 'https://example.daily.co/room123';
			const token = 'test-token';
			mockCallObject.join.mockRejectedValue(new Error('Join failed'));

			const result = await dailyService.initializeCall(roomUrl, token);

			expect(result).toBe(false);
		});
	});

	describe('event listeners', () => {
		it('should set up and remove event listeners', async () => {
			await dailyService.initializeCall('https://example.daily.co/room123', 'token');

			// Check that all event listeners were attached
			expect(mockCallObject.on).toHaveBeenCalledWith('participant-joined', expect.any(Function));
			expect(mockCallObject.on).toHaveBeenCalledWith('participant-updated', expect.any(Function));
			expect(mockCallObject.on).toHaveBeenCalledWith('participant-left', expect.any(Function));
			expect(mockCallObject.on).toHaveBeenCalledWith('error', expect.any(Function));
			expect(mockCallObject.on).toHaveBeenCalledWith('left-meeting', expect.any(Function));
			expect(mockCallObject.on).toHaveBeenCalledWith('app-message', expect.any(Function));
			expect(mockCallObject.on).toHaveBeenCalledWith('network-quality', expect.any(Function));

			await dailyService.endCall();

			// Check that all event listeners were detached
			expect(mockCallObject.off).toHaveBeenCalledWith('participant-joined', expect.any(Function));
			expect(mockCallObject.off).toHaveBeenCalledWith('participant-updated', expect.any(Function));
			expect(mockCallObject.off).toHaveBeenCalledWith('participant-left', expect.any(Function));
			expect(mockCallObject.off).toHaveBeenCalledWith('error', expect.any(Function));
			expect(mockCallObject.off).toHaveBeenCalledWith('left-meeting', expect.any(Function));
			expect(mockCallObject.off).toHaveBeenCalledWith('app-message', expect.any(Function));
			expect(mockCallObject.off).toHaveBeenCalledWith('network-quality', expect.any(Function));
		});
	});

	describe('media controls', () => {
		beforeEach(async () => {
			await dailyService.initializeCall('https://example.daily.co/room123', 'token');
		});

		it('should toggle audio', async () => {
			const initialAudioState = dailyService.isAudioEnabled;
			const result = await dailyService.toggleAudio();
			expect(result).toBe(true);
			expect(mockCallObject.setLocalAudio).toHaveBeenCalledWith(!initialAudioState);
			expect(dailyService.isAudioEnabled).toBe(!initialAudioState);
		});

		it('should toggle video', async () => {
			const initialVideoState = dailyService.isVideoEnabled;
			const result = await dailyService.toggleVideo();
			expect(result).toBe(true);
			expect(mockCallObject.setLocalVideo).toHaveBeenCalledWith(!initialVideoState);
			expect(dailyService.isVideoEnabled).toBe(!initialVideoState);
		});

		it('should enable video', async () => {
			const result = await dailyService.enableVideo();
			expect(result).toBe(true);
			expect(mockCallObject.setLocalVideo).toHaveBeenCalledWith(true);
			expect(dailyService.isVideoEnabled).toBe(true);
		});

		it('should start screen share if not already sharing', async () => {
			mockCallObject.participants.mockReturnValue({ local: { screen: false } as Partial<DailyParticipant> });
			const result = await dailyService.shareScreen();
			expect(result).toBe(true);
			expect(mockCallObject.startScreenShare).toHaveBeenCalled();
			expect(dailyService.isScreenSharing).toBe(true);
		});

		it('should stop screen share if already sharing', async () => {
			mockCallObject.participants.mockReturnValue({ local: { screen: true } as Partial<DailyParticipant> });
			const result = await dailyService.shareScreen();
			expect(result).toBe(true);
			expect(mockCallObject.stopScreenShare).toHaveBeenCalled();
			expect(dailyService.isScreenSharing).toBe(false);
		});
	});

	describe('app messages', () => {
		beforeEach(async () => {
			await dailyService.initializeCall('https://example.daily.co/room123', 'token');
		});

		it('should send a video request app message', async () => {
			const result = await dailyService.sendVideoRequest();
			expect(result).toBe(true);
			expect(mockCallObject.sendAppMessage).toHaveBeenCalledWith({ type: 'video-request' }, '*');
		});

		it('should send a video request response app message', async () => {
			const result = await dailyService.sendVideoRequestResponse(true);
			expect(result).toBe(true);
			expect(mockCallObject.sendAppMessage).toHaveBeenCalledWith({ type: 'video-request-response', accepted: true }, '*');
		});
	});

	describe('callbacks', () => {
		beforeEach(async () => {
			await dailyService.initializeCall('https://example.daily.co/room123', 'token');
		});

		it('should handle participant updates', () => {
			const callback = vi.fn<[{ [id: string]: DailyParticipant }]>();
			dailyService.onParticipantsChange(callback);

			const participant: DailyParticipant = {
				session_id: 'p1',
				user_name: 'Participant 1',
				video: true,
				audio: true,
				screen: false,
				local: false,
				owner: false,
				record: false,
				user_id: 'user1',
				tracks: {
					audio: { state: 'playable', subscribed: true },
					video: { state: 'playable', subscribed: true },
					screenAudio: { state: 'loading', subscribed: false },
					screenVideo: { state: 'loading', subscribed: false }
				},
				will_eject_at: new Date(),
				permissions: { canSend: true, hasPresence: true, canAdmin: false },
				cam_info: {},
				screen_info: {}
			};

			const event: DailyEventObjectParticipant = { action: 'participant-joined', participant, callClientId: 'test-id' };
			const handler = eventHandlers['participant-joined'];
			expect(handler).toBeDefined();
			if (handler) {
				handler(event);
			}

			expect(callback).toHaveBeenCalledWith(
				expect.objectContaining({
					p1: expect.objectContaining({ session_id: 'p1' })
				})
			);
		});

		it('should handle errors', () => {
			const callback = vi.fn<[DailyEventObjectFatalError]>();
			dailyService.onErrorOccurred(callback);

			const error: DailyEventObjectFatalError = { action: 'error', error: { type: 'fatal', msg: 'A fatal error occurred' } };
			const handler = eventHandlers['error'];
			expect(handler).toBeDefined();
			if (handler) {
				handler(error);
			}

			expect(callback).toHaveBeenCalledWith(error);
		});
	});

	describe('endCall', () => {
		it('should leave and destroy the call object', async () => {
			await dailyService.initializeCall('https://example.daily.co/room123', 'token');
			await dailyService.endCall();
			expect(mockCallObject.leave).toHaveBeenCalled();
			expect(mockCallObject.destroy).toHaveBeenCalled();
		});
	});

	describe('getCallObject', () => {
		it('should return the call object', async () => {
			await dailyService.initializeCall('https://example.daily.co/room123', 'token');
			const callObject = dailyService.getCallObject();
			expect(callObject).toBe(mockCallObject);
		});
	});
});
