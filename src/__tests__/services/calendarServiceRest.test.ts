import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  calendarService,
  AvailabilityResult,
  WeeklyAvailabilityDay,
} from '../../lib/services/calendarServiceRest';
import { calendarApi, DayAvailability, WeeklyAvailability } from '../../api/calendar';
import { ApiResponse } from '../../api/client';

// Mock the calendarApi
vi.mock('../../api/calendar');

describe('calendarService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getAvailableTimeSlots', () => {
    it('should return available time slots on success', async () => {
      const mockProviderId = 'provider-1';
      const mockDate = new Date('2024-01-01T00:00:00.000Z');
      const mockApiResponse: ApiResponse<DayAvailability> = {
        success: true,
        data: {
          date: '2024-01-01',
          isAvailable: true,
          workingHours: { start: '09:00', end: '17:00' },
          timeSlots: [
            { startTime: '2024-01-01T09:00:00.000Z', endTime: '2024-01-01T09:30:00.000Z', isAvailable: true },
            { startTime: '2024-01-01T10:00:00.000Z', endTime: '2024-01-01T10:30:00.000Z', isAvailable: true },
          ],
        },
      };

      vi.mocked(calendarApi.getDayAvailability).mockResolvedValue(mockApiResponse);

      const result: AvailabilityResult = await calendarService.getAvailableTimeSlots(
        mockProviderId,
        mockDate
      );

      expect(result.success).toBe(true);
      expect(result.timeSlots).toHaveLength(2);
      expect(result.timeSlots?.[0].startTime).toEqual(new Date('2024-01-01T09:00:00.000Z'));
      expect(calendarApi.getDayAvailability).toHaveBeenCalledWith(mockProviderId, '2024-01-01');
    });

    it('should return failure when API call fails', async () => {
      const mockProviderId = 'provider-1';
      const mockDate = new Date('2024-01-01T00:00:00.000Z');
      const mockApiResponse: ApiResponse<DayAvailability> = { success: false, error: 'API Error' };

      vi.mocked(calendarApi.getDayAvailability).mockResolvedValue(mockApiResponse);

      const result: AvailabilityResult = await calendarService.getAvailableTimeSlots(
        mockProviderId,
        mockDate
      );

      expect(result.success).toBe(false);
      expect(result.message).toBe('API Error');
    });
  });

  describe('getProviderWeeklyAvailability', () => {
    it('should return weekly availability data on success', async () => {
      const mockProviderId = 'provider-1';
      const mockStartDate = new Date('2024-01-01T00:00:00.000Z');
      const mockApiResponse: ApiResponse<WeeklyAvailability> = {
        success: true,
        data: [
          { date: '2024-01-01', dayOfWeek: 1, isAvailable: true, startTime: '09:00', endTime: '17:00' },
          { date: '2024-01-02', dayOfWeek: 2, isAvailable: false, startTime: null, endTime: null },
        ],
      };

      vi.mocked(calendarApi.getProviderWeeklyAvailability).mockResolvedValue(mockApiResponse);

      const result: WeeklyAvailabilityDay[] =
        await calendarService.getProviderWeeklyAvailability(mockProviderId, mockStartDate);

      expect(result).toHaveLength(2);
      expect(result[0].date).toEqual(new Date('2024-01-01T00:00:00.000Z'));
      expect(result[0].isAvailable).toBe(true);
      expect(calendarApi.getProviderWeeklyAvailability).toHaveBeenCalledWith(
        mockProviderId,
        mockStartDate
      );
    });

    it('should return an empty array when API call fails', async () => {
      const mockProviderId = 'provider-1';
      const mockStartDate = new Date('2024-01-01T00:00:00.000Z');
      const mockApiResponse: ApiResponse<WeeklyAvailability> = { success: false, error: 'API Error' };

      vi.mocked(calendarApi.getProviderWeeklyAvailability).mockResolvedValue(mockApiResponse);

      const result: WeeklyAvailabilityDay[] =
        await calendarService.getProviderWeeklyAvailability(mockProviderId, mockStartDate);

      expect(result).toEqual([]);
    });
  });
});

