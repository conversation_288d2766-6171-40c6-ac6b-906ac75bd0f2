import { describe, it, expect, beforeEach, vi } from 'vitest';
import { type Consultation } from '../../api/consultations';
import { consultationService } from '../../lib/services/consultationService';
import { apiClient } from '../../api/client';
import { consultationsApi, type UpdateConsultationData } from '../../api/consultations';

// Mock API clients
vi.mock('../../api/client');
vi.mock('../../api/consultations');

describe('consultationService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const mockConsultation: Consultation = {
    id: 'consultation-123',
    appointmentId: 'appointment-123',
    status: 'SCHEDULED',
    roomUrl: 'https://example.daily.co/room123',
    videoEnabled: true,
    notes: '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const mockApiResponse = <T>(data: T) => ({ success: true, data });

  describe('getConsultationById', () => {
    it('should call the API and return the result', async () => {
      const response = mockApiResponse(mockConsultation);
      vi.mocked(consultationsApi.getConsultation).mockResolvedValue(response);
      const result = await consultationService.getConsultationById('consultation-123');
      expect(result).toEqual(response);
      expect(consultationsApi.getConsultation).toHaveBeenCalledWith('consultation-123');
    });
  });

  describe('getConsultationByAppointmentId', () => {
    it('should call the API and return the result', async () => {
      const response = mockApiResponse(mockConsultation);
      vi.mocked(consultationsApi.getConsultationByAppointment).mockResolvedValue(response);
      const result = await consultationService.getConsultationByAppointmentId('appointment-123');
      expect(result).toEqual(response);
      expect(consultationsApi.getConsultationByAppointment).toHaveBeenCalledWith('appointment-123');
    });
  });

  describe('createConsultationRoom', () => {
    it('should call the API and return the result', async () => {
      const response = mockApiResponse({ url: 'https://example.daily.co/new-room' });
      vi.mocked(apiClient.post).mockResolvedValue(response);
      const result = await consultationService.createConsultationRoom('appointment-123');
      expect(result).toEqual(response);
      expect(apiClient.post).toHaveBeenCalledWith('/consultations/room', { appointmentId: 'appointment-123' });
    });
  });

  describe('generateRoomToken', () => {
    it('should call the API and return the result', async () => {
      const response = mockApiResponse({ token: 'test-token' });
      const userId = 'user-123';
      vi.mocked(consultationsApi.generateToken).mockResolvedValue(response);
      const result = await consultationService.generateRoomToken('consultation-123', userId);
      expect(result).toEqual(response);
      expect(consultationsApi.generateToken).toHaveBeenCalledWith('consultation-123', { userId });
    });
  });

  describe('createConsultation', () => {
    it('should call the API and return the result', async () => {
      const response = mockApiResponse(mockConsultation);
      const appointmentId = 'appointment-456';
      vi.mocked(consultationsApi.createConsultation).mockResolvedValue(response);
      const result = await consultationService.createConsultation(appointmentId);
      expect(result).toEqual(response);
      expect(consultationsApi.createConsultation).toHaveBeenCalledWith({ appointmentId });
    });
  });

  describe('updateConsultationStatus', () => {
    it('should call the API and return the result', async () => {
      const updatedConsultation = { ...mockConsultation, status: 'IN_PROGRESS' as const };
      const response = mockApiResponse(updatedConsultation);
      vi.mocked(consultationsApi.updateConsultation).mockResolvedValue(response);
      const result = await consultationService.updateConsultationStatus('consultation-123', 'IN_PROGRESS');
      expect(result).toEqual(response);
      expect(consultationsApi.updateConsultation).toHaveBeenCalledWith('consultation-123', { status: 'IN_PROGRESS' });
    });
  });

  describe('updateConsultation', () => {
    it('should call the API and return the result', async () => {
      const dataToUpdate: UpdateConsultationData = { notes: 'Updated notes' };
      const updatedConsultation = { ...mockConsultation, ...dataToUpdate };
      const response = mockApiResponse(updatedConsultation);

      vi.mocked(consultationsApi.updateConsultation).mockResolvedValue(response);
      const result = await consultationService.updateConsultation('consultation-123', dataToUpdate);

      expect(result).toEqual(response);
      expect(consultationsApi.updateConsultation).toHaveBeenCalledWith('consultation-123', dataToUpdate);
    });
  });
});

