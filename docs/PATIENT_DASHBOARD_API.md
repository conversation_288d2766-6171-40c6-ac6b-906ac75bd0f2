# Patient Dashboard API Documentation

This document outlines the API endpoints and data models for the patient-facing dashboard. These endpoints are designed to provide patients with access to their profile information, appointment history, and medical records.

## Authentication

All endpoints require the user to be authenticated as a `PATIENT`. The patient's ID will be derived from the authenticated user's session.

---

## Data Models

### PatientProfile

Represents the patient's user profile information.

```json
{
  "id": "string",
  "name": "string",
  "email": "string",
  "emailVerified": "DateTime | null",
  "image": "string | null",
  "phone": "string | null"
}
```

### AppointmentSummary

A brief summary of an appointment, used for lists.

```json
{
  "id": "string",
  "appointmentDate": "DateTime",
  "status": "string", // e.g., SCHEDULED, COMPLETED, CANCELED
  "consultationType": "string", // e.g., VIDEO, AUDIO
  "provider": {
    "id": "string",
    "name": "string",
    "specialization": "string | null"
  }
}
```

### AppointmentDetails

Detailed information about a single appointment.

```json
{
  "id": "string",
  "appointmentDate": "DateTime",
  "status": "string",
  "reason": "string | null",
  "consultationType": "string",
  "provider": {
    "id": "string",
    "name": "string",
    "specialization": "string | null",
    "bio": "string | null"
  },
  "consultation": {
    "id": "string",
    "roomUrl": "string",
    "status": "string",
    "notes": "string | null"
  } | null
}
```

### MedicalRecord

Represents a patient's medical record (placeholder for future implementation).

```json
{
  "id": "string",
  "recordDate": "DateTime",
  "type": "string", // e.g., 'ConsultationNote', 'LabResult'
  "title": "string",
  "summary": "string",
  "attachmentUrl": "string | null"
}
```

---

## API Endpoints

### Patient Profile

#### 1. Get Patient Profile

-   **Endpoint:** `GET /api/patient/profile`
-   **Description:** Retrieves the profile information for the currently authenticated patient.
-   **Response (200 OK):**
    -   **Body:** `PatientProfile`

#### 2. Update Patient Profile

-   **Endpoint:** `PUT /api/patient/profile`
-   **Description:** Updates the profile information for the currently authenticated patient.
-   **Request Body:**
    ```json
    {
      "name": "string",
      "phone": "string"
    }
    ```
-   **Response (200 OK):**
    -   **Body:** `PatientProfile` (the updated profile)

---

### Appointments

#### 3. Get Appointment History

-   **Endpoint:** `GET /api/patient/appointments`
-   **Description:** Retrieves a list of all appointments for the currently authenticated patient. Can be filtered by status.
-   **Query Parameters:**
    -   `status` (optional, string): Filter by status (e.g., `SCHEDULED`, `COMPLETED`, `CANCELED`). If not provided, returns all appointments.
-   **Response (200 OK):**
    -   **Body:** `AppointmentSummary[]`

#### 4. Get Appointment Details

-   **Endpoint:** `GET /api/patient/appointments/:id`
-   **Description:** Retrieves detailed information for a specific appointment.
-   **URL Parameters:**
    -   `id` (required, string): The ID of the appointment.
-   **Response (200 OK):**
    -   **Body:** `AppointmentDetails`

---

### Medical Records

#### 5. Get Medical Records

-   **Endpoint:** `GET /api/patient/records`
-   **Description:** Retrieves a list of medical records for the currently authenticated patient.
-   **Status:** Placeholder for future implementation.
-   **Response (200 OK):**
    -   **Body:** `MedicalRecord[]`