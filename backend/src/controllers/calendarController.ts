import { Request, Response } from 'express';
import prisma from '../config/database';
import { DayAvailability, TimeSlot, WeeklyAvailability } from '../types/calendar';

export const getDayAvailability = async (req: Request, res: Response): Promise<void> => {
  const { providerId } = req.params;
  const { date: dateString } = req.query;

  if (!providerId) {
    res.status(400).json({ success: false, error: 'Provider ID is required' });
    return;
  }

  if (!dateString || typeof dateString !== 'string') {
    res.status(400).json({ success: false, error: 'Date query parameter is required' });
    return;
  }

  try {
    const date = new Date(dateString);
    const dayOfWeek = date.getDay();

    const availability = await prisma.availability.findFirst({
      where: {
        providerId,
        dayOfWeek,
        isAvailable: true,
      },
    });

    if (!availability) {
      const response: DayAvailability = {
        date: dateString,
        isAvailable: false,
        timeSlots: [],
        workingHours: { start: '', end: '' },
      };
      res.status(200).json({ success: true, data: response });
      return;
    }

    if (!availability.startTime || !availability.endTime) {
      res.status(500).json({ success: false, error: 'Provider availability times are not set' });
      return;
    }

    const [startHour, startMinute] = availability.startTime.split(':').map(Number);
    const [endHour, endMinute] = availability.endTime.split(':').map(Number);

    if (isNaN(startHour) || isNaN(startMinute) || isNaN(endHour) || isNaN(endMinute)) {
      res.status(500).json({ success: false, error: 'Invalid availability time format' });
      return;
    }

    const startDate = new Date(date);
    startDate.setUTCHours(startHour, startMinute, 0, 0);

    const endDate = new Date(date);
    endDate.setUTCHours(endHour, endMinute, 0, 0);

    const appointments = await prisma.appointment.findMany({
      where: {
        providerId,
        appointmentDate: {
          gte: new Date(date.setUTCHours(0, 0, 0, 0)),
          lt: new Date(date.setUTCHours(23, 59, 59, 999)),
        },
        status: {
          in: ['SCHEDULED', 'CONFIRMED', 'IN_PROGRESS'],
        },
      },
    });

    const timeSlots: TimeSlot[] = [];
    const slotDuration = 30; // 30-minute slots

    for (let currentTime = new Date(startDate); currentTime < endDate; currentTime.setMinutes(currentTime.getMinutes() + slotDuration)) {
      const slotStart = new Date(currentTime);
      const slotEnd = new Date(currentTime.getTime() + slotDuration * 60000);

      const isBooked = appointments.some(app => {
        const appStart = new Date(app.appointmentDate);
        const appEnd = new Date(appStart.getTime() + 30 * 60000); // Assuming 30 min appointments
        return slotStart < appEnd && slotEnd > appStart;
      });

      timeSlots.push({
        startTime: slotStart.toISOString(),
        endTime: slotEnd.toISOString(),
        isAvailable: !isBooked,
      });
    }

    const response: DayAvailability = {
      date: dateString,
      isAvailable: true,
      timeSlots,
      workingHours: {
        start: availability.startTime,
        end: availability.endTime,
      },
    };

    res.status(200).json({ success: true, data: response });
  } catch (error) {
    console.error('Error fetching day availability:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch availability' });
  }
};

export const getProviderWeeklyAvailability = async (req: Request, res: Response): Promise<void> => {
  const { providerId } = req.params;
  const { startDate: startDateString } = req.query;

  if (!providerId) {
    res.status(400).json({ success: false, error: 'Provider ID is required' });
    return;
  }

  if (!startDateString || typeof startDateString !== 'string') {
    res.status(400).json({ success: false, error: 'startDate query parameter is required' });
    return;
  }

  try {
    const startDate = new Date(startDateString);
    const weekDates = Array.from({ length: 7 }).map((_, i) => {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      return date;
    });

    const availabilities = await prisma.availability.findMany({
      where: {
        providerId,
        dayOfWeek: {
          in: weekDates.map(d => d.getDay()),
        },
      },
    });

    const weeklyAvailability: WeeklyAvailability = weekDates.map(date => {
      const dayOfWeek = date.getDay();
      const availability = availabilities.find(a => a.dayOfWeek === dayOfWeek);

      return {
        date: date.toISOString(),
        dayOfWeek,
        isAvailable: !!availability?.isAvailable,
        startTime: availability?.startTime || null,
        endTime: availability?.endTime || null,
      };
    });

    res.status(200).json({ success: true, data: weeklyAvailability });
  } catch (error) {
    console.error('Error fetching weekly availability:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch weekly availability' });
  }
};
