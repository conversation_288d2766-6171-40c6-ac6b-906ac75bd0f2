import { Request, Response } from 'express';
import { prisma } from '@/config/database';
import { paystackService } from '@/services/paystackService';
import logger from '@/config/logger';

export const initializePaystackPayment = async (req: Request, res: Response): Promise<void> => {
	try {
		const { appointmentId, amount, email } = req.body as { appointmentId: string; amount: number; email: string };

		if (!appointmentId || !amount || !email) {
			res.status(400).json({ success: false, error: 'appointmentId, amount, email required' });
			return;
		}

		// Confirm appointment exists
		const appointment = await prisma.appointment.findUnique({ where: { id: appointmentId } });
		if (!appointment) {
			res.status(404).json({ success: false, error: 'Appointment not found' });
			return;
		}

		const init = await paystackService.initializePayment(amount * 100, email, { appointmentId });
		res.status(200).json({ success: true, data: init });
	} catch (error) {
		logger.error('Initialize Paystack error', error);
		res.status(500).json({ success: false, error: 'Failed to initialize payment' });
	}
};

export const paystackWebhook = async (req: Request, res: Response): Promise<void> => {
	try {
		const sig = req.headers['x-paystack-signature'] as string | undefined;
		if (!sig) {
			res.status(400).end();
			return;
		}

		const rawBody: string = Buffer.isBuffer(req.body) ? req.body.toString('utf8') : typeof req.body === 'string' ? req.body : JSON.stringify(req.body);
		if (!paystackService.verifySignature(rawBody, sig)) {
			logger.warn('Invalid Paystack signature');
			res.status(400).end();
			return;
		}
		const event = JSON.parse(rawBody);

		if (event.event === 'charge.success') {
			const { reference, metadata } = event.data;
			const appointmentId: string | undefined = metadata?.appointmentId;
			if (appointmentId) {
				const appointment = await prisma.appointment.findUnique({ where: { id: appointmentId }, select: { patientId: true } });
				if (appointment) {
					await prisma.payment.create({
						data: {
							paymentMethod: 'PAYSTACK',
							transactionId: reference,
							amount: event.data.amount / 100,
							status: 'COMPLETED',
							appointmentId,
							patientId: appointment.patientId,
							currency: event.data.currency,
						},
					});
				}
			}
		}

		res.status(200).end();
	} catch (error) {
		logger.error('Paystack webhook error', error);
		res.status(500).end();
	}
};

export const getPaystackInvoice = async (req: Request, res: Response): Promise<void> => {
	try {
		const { reference } = req.params as { reference: string };
		if (!reference) {
			res.status(400).json({ success: false, error: 'reference required' });
			return;
		}
		const tx = await paystackService.verifyTransaction(reference);
		res.status(200).json({ success: true, data: tx });
	} catch (error) {
		logger.error('Get Paystack invoice error', error);
		res.status(500).json({ success: false, error: 'Failed to retrieve invoice' });
	}
};
