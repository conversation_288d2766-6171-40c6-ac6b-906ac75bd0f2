import { Request, Response, NextFunction } from 'express';

import { User } from '@prisma/client';

declare module 'express-serve-static-core' {
  interface Request {
    user?: User;
  }
}

export const authenticateToken = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization'];

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    res.status(401).json({ success: false, error: 'Authentication required' });
    return;
  }

  const token = authHeader.substring(7);

  // TODO: Verify JWT token and attach user to request.
  // For now, simply proceed to next middleware assuming token is valid.
  if (!token) {
    res.status(401).json({ success: false, error: 'Authentication required' });
    return;
  }

  // Token exists – proceed (verification to be added later)
  next();
};

export const authorizeRoles = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const userRole = req.user?.role;
    if (!userRole || !roles.includes(userRole)) {
      res.status(403).json({ success: false, error: 'Forbidden' });
      return;
    }
    next();
  };
};

// Alias for existing route imports
export const authorize = authorizeRoles;
export const protect = authenticateToken;
