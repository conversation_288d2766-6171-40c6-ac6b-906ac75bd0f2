import axios, { AxiosInstance } from 'axios';
import crypto from 'node:crypto';
import { config } from '@/config';
import logger from '@/config/logger';

interface PaystackInitializeResponse {
	status: boolean;
	message: string;
	data: {
		authorization_url: string;
		access_code: string;
		reference: string;
	};
}

/**
 * Minimal shape of a Paystack transaction as returned by the Verify endpoint.
 * Only commonly-used fields are included; extra fields are captured via `[key: string]: unknown`.
 */
interface PaystackTransaction {
	id: number;
	status: string;
	reference: string;
	amount: number;
	currency: string;
	metadata: Record<string, unknown>;
	paid_at: string | null;
	created_at: string;
	customer: Record<string, unknown>;
	[key: string]: unknown;
}

interface PaystackVerifyResponse {
	status: boolean;
	message: string;
	data: PaystackTransaction;
}

class PaystackService {
	private client: AxiosInstance;

	constructor() {
		if (!config.payments.paystack.secretKey) {
			throw new Error('PAYSTACK_SECRET_KEY not configured');
		}
		this.client = axios.create({
			baseURL: 'https://api.paystack.co',
			headers: {
				Authorization: `Bearer ${config.payments.paystack.secretKey}`,
				'Content-Type': 'application/json',
			},
			timeout: config.payments.paystack.timeout,
		});
	}

	async initializePayment(amountKobo: number, email: string, metadata: Record<string, unknown>): Promise<PaystackInitializeResponse['data']> {
		try {
			const res = await this.client.post<PaystackInitializeResponse>('/transaction/initialize', {
				amount: amountKobo,
				email,
				metadata,
			});
			if (!res.data.status) {
				throw new Error(res.data.message);
			}
			return res.data.data;
		} catch (error: unknown) {
			logger.error('Paystack initialize error', error);
			throw new Error('Failed to initialize Paystack payment');
		}
	}

	async verifyTransaction(reference: string): Promise<PaystackTransaction> {
		try {
			const res = await this.client.get<PaystackVerifyResponse>(`/transaction/verify/${reference}`);
			if (!res.data.status) {
				throw new Error(res.data.message);
			}
			return res.data.data;
		} catch (error: unknown) {
			logger.error('Paystack verify transaction error', error);
			throw new Error('Failed to retrieve transaction');
		}
	}

	verifySignature(rawBody: string, receivedSig: string): boolean {
		const hash = crypto.createHmac('sha512', config.payments.paystack.secretKey!).update(rawBody).digest('hex');
		return hash === receivedSig;
	}
}

export const paystackService = new PaystackService();
