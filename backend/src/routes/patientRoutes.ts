import express from 'express';
import { getPatientProfile, updatePatientProfile, getAppointmentHistory, getAppointmentDetails, getMedicalRecords } from '@/controllers/patientController';
import { protect, authorize } from '@/middleware/auth';

const router = express.Router();

// All routes in this file are protected and restricted to PATIENT role
router.use(protect);
router.use(authorize('PATIENT'));

router.get('/profile', getPatientProfile);
router.put('/profile', updatePatientProfile);
router.get('/appointments', getAppointmentHistory);
router.get('/appointments/:id', getAppointmentDetails);
router.get('/records', getMedicalRecords);

export default router;