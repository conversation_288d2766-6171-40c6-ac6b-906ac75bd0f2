import { Router } from 'express';
import express from 'express';
import {
  createPaymentIntent,
  confirmPayment,
  getPayment,
  getPayments,
  handleStripeWebhook
} from '@/controllers/paymentController';
import { initializePaystackPayment, paystackWebhook, getPaystackInvoice } from '@/controllers/paystackController';
import { authenticateToken, authorize } from '@/middleware/auth';

const router = Router();

/**
 * @route   POST /api/payments/intent
 * @desc    Create payment intent for appointment
 * @access  Private (Patient only)
 */
router.post(
  '/intent',
  authenticateToken,
  authorize('PATIENT'),
  createPaymentIntent
);

/**
 * @route   POST /api/payments/confirm
 * @desc    Confirm payment
 * @access  Private (Patient only)
 */
router.post(
  '/confirm',
  authenticateToken,
  authorize('PATIENT'),
  confirmPayment
);

/**
 * @route   GET /api/payments/:id
 * @desc    Get payment details
 * @access  Private (Patient, Provider, Admin)
 */
router.get(
  '/:id',
  authenticateToken,
  getPayment
);

/**
 * @route   GET /api/payments
 * @desc    Get user's payments
 * @access  Private
 */
router.get(
  '/',
  authenticateToken,
  getPayments
);

/**
 * @route   POST /api/payments/webhook
 * @desc    Handle Stripe webhook events
 * @access  Public (Stripe webhook)
 */
router.post(
  '/webhook',
  handleStripeWebhook
);

/**
 * @route   POST /api/payments/paystack/initialize
 * @desc    Initialize Paystack payment
 * @access  Private (Patient only)
 */
router.post(
  '/paystack/initialize',
  authenticateToken,
  authorize('PATIENT'),
  initializePaystackPayment
);

/**
 * @route   POST /api/payments/paystack/webhook
 * @desc    Handle Paystack webhook events
 * @access  Public (Paystack)
 */
router.post(
  '/paystack/webhook',
  express.raw({ type: 'application/json' }),
  paystackWebhook
);

/**
 * @route   GET /api/payments/paystack/invoice/:reference
 * @desc    Retrieve Paystack transaction/invoice details
 * @access  Private (Patient or Admin)
 */
router.get(
  '/paystack/invoice/:reference',
  authenticateToken,
  authorize('PATIENT', 'ADMIN'),
  getPaystackInvoice
 );

export default router;
