import { Router } from 'express';
import { getDayAvailability, getProviderWeeklyAvailability } from '../controllers/calendarController';
import { protect } from '../middleware/auth';

const router = Router();

// @desc    Get availability for a specific day for a provider
// @route   GET /api/calendar/availability/:providerId
// @access  Private
router.get('/availability/day/:providerId', protect, getDayAvailability);
router.get('/availability/week/:providerId', protect, getProviderWeeklyAvailability);

export default router;
