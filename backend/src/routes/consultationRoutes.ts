import { Router } from 'express';
import { 
  createConsultationRoom,
  joinConsultation,
  generateConsultationToken,
  getConsultation,
  getConsultationNotes,
  updateConsultationNotes,
  getConsultationRecordings
} from '@/controllers/consultationController';
import { authenticateToken } from '@/middleware/auth';

const router = Router();

/**
 * @route   POST /api/consultations/room
 * @desc    Create Daily.co room for consultation
 * @access  Private (Patient, Provider, Admin)
 */
router.post(
  '/room',
  authenticateToken,
  createConsultationRoom
);

/**
 * @route   POST /api/consultations/:appointmentId/join
 * @desc    Generate Daily.co token for participant
 * @access  Private (Patient, Provider, Admin)
 */
router.post(
  '/:appointmentId/join',
  authenticateToken,
  joinConsultation
);

/**
 * @route   GET /api/consultations/:appointmentId
 * @desc    Get consultation details
 * @access  Private (Patient, Provider, Admin)
 */
router.get(
  '/:appointmentId',
  authenticateToken,
  getConsultation
);

/**
 * @route   GET /api/consultations/:appointmentId/notes
 * @desc    Get consultation notes
 * @access  Private (Patient, Provider, Admin)
 */
router.get(
  '/:appointmentId/notes',
  authenticateToken,
  getConsultationNotes
);

/**
 * @route   PATCH /api/consultations/:appointmentId/notes
 * @desc    Update consultation notes
 * @access  Private (Provider, Admin)
 */
router.patch(
  '/:appointmentId/notes',
  authenticateToken,
  updateConsultationNotes
);

/**
 * @route   GET /api/consultations/:appointmentId/recordings
 * @desc    Get Daily.co recordings for consultation
 * @access  Private (Patient, Provider, Admin)
 */
router.get(
  '/:appointmentId/recordings',
  authenticateToken,
  getConsultationRecordings
);

export default router;
