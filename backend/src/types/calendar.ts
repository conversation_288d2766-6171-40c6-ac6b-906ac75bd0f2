// Types for the Calendar API responses

export interface TimeSlot {
  startTime: string; // ISO string
  endTime: string; // ISO string
  isAvailable: boolean;
}

export interface DayAvailability {
  date: string;
  isAvailable: boolean;
  timeSlots: TimeSlot[];
  workingHours: {
    start: string;
    end: string;
  };
}

export type WeeklyAvailability = {
  date: string; // ISO string
  dayOfWeek: number;
  isAvailable: boolean;
  startTime: string | null;
  endTime: string | null;
}[];
