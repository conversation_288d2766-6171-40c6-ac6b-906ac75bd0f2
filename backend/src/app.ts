import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { config } from '@/config';
import logger from '@/config/logger';
import { errorHandler, notFoundHandler } from '@/middleware/errorHandler';
import { rateLimiters } from '@/middleware/rateLimiter';

// Import routes
import patientRoutes from '@/routes/patientRoutes';
import schedulingRoutes from '@/routes/schedulingRoutes';
import consultationRoutes from '@/routes/consultationRoutes';
import paymentRoutes from '@/routes/paymentRoutes';
import authRoutes from '@/routes/authRoutes';
import userRoutes from '@/routes/userRoutes';
import appointmentRoutes from './routes/appointments';
import calendarRoutes from './routes/calendar';

import onboardingRoutes from '@/routes/onboardingRoutes';
import adminRoutes from '@/routes/adminRoutes';

const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Security middleware
app.use(helmet());

// CORS configuration
app.use(
  cors({
    origin: config.frontend.corsOrigins,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  })
);

// Body parsing
app.use(express.json({ limit: '10kb' }));
app.use(express.urlencoded({ extended: true, limit: '10kb' }));

// Request logging
if (config.server.isDevelopment) {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }));
}

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/appointments', appointmentRoutes);
app.use('/api/calendar', calendarRoutes);
app.use('/api/consultations', consultationRoutes);
app.use('/api/payments', rateLimiters.api, paymentRoutes);
app.use('/api/patients/onboarding', rateLimiters.api, onboardingRoutes);
app.use('/api/admin', rateLimiters.admin, adminRoutes);
app.use('/api/scheduling', schedulingRoutes);
app.use('/api/patient', patientRoutes);

// Temporary test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'Dr. Fintan Virtual Care Hub Backend API is running!',
    timestamp: new Date().toISOString(),
    environment: config.server.nodeEnv,
  });
});

// 404 handler
app.use(notFoundHandler);

// Error handling middleware (must be last)
app.use(errorHandler);

export default app;
