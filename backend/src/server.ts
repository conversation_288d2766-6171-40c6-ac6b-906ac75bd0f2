import { createServer } from 'http';
import app from '@/app';
import { config } from '@/config';
import { checkDatabaseConnection, disconnectDatabase } from '@/config/database';
import logger from '@/config/logger';

// Create HTTP server from the Express app
const server = createServer(app);

// Start the server and verify database connectivity
const startServer = async (): Promise<void> => {
	try {
		await checkDatabaseConnection();
		server.listen(config.server.port, () => {
			logger.info(`🚀 Server listening on port ${config.server.port} (${config.server.nodeEnv})`);
		});
	} catch (error) {
		logger.error('Failed to start server:', error);
		process.exit(1);
	}
};

// Graceful shutdown helper
const gracefulShutdown = (signal: string): void => {
	logger.info(`${signal} received. Shutting down gracefully...`);
	server.close(async () => {
		await disconnectDatabase();
		logger.info('HTTP server closed. Bye!');
		process.exit(0);
	});

	// Force-exit if shutdown hangs
	setTimeout(() => {
		logger.error('Forced shutdown after 10 s');
		process.exit(1);
	}, 10_000);
};

// OS signals & unexpected errors
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('uncaughtException', (err) => {
	logger.error('Uncaught exception:', err);
	gracefulShutdown('uncaughtException');
});
process.on('unhandledRejection', (reason) => {
	logger.error('Unhandled rejection:', reason);
	gracefulShutdown('unhandledRejection');
});

// Kick things off
void startServer();

export default server;
