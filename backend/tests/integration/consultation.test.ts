import request from 'supertest';
import app from '@/app';
import { prisma } from '@/config/database';
import { disconnectDatabase } from '@/config/database';
import { User, Patient, Provider, Appointment } from '@prisma/client';
import { authenticateToken } from '@/middleware/auth';

// Mock the authentication middleware
jest.mock('@/middleware/auth', () => ({
  ...jest.requireActual('@/middleware/auth'),
  authenticateToken: jest.fn((req, res, next) => {
    // This mock will be customized inside each test
    next();
  }),
}));

// Increase default Jest timeout for slower DB operations
jest.setTimeout(30000);

describe('Consultation Routes', () => {
  let testUser: User;
  let testPatient: Patient & { user: User };
  let testProvider: Provider & { user: User };
  let testAppointment: Appointment;

  beforeAll(async () => {
    // Create shared test data
    testUser = await prisma.user.create({
      data: {
        email: `test-patient-${Date.now()}@example.com`,
        name: 'Test Patient',
        role: 'PATIENT',
        phone: '**********',
      },
    });

    testPatient = (await prisma.patient.create({
      data: {
        user: {
          connect: { id: testUser.id },
        },
        dateOfBirth: new Date('1990-01-01'),
        phone: '**********',
        address: '123 Test St',
      },
      include: { user: true },
    })) as Patient & { user: User };

    const providerUser = await prisma.user.create({
      data: {
        email: `test-provider-${Date.now()}@example.com`,
        name: 'Test Provider',
        role: 'PROVIDER',
      },
    });

    testProvider = await prisma.provider.create({
      data: {
        userId: providerUser.id,
        specialization: 'TESTING',
        licenseNumber: '12345',
      },
      include: { user: true },
    });

    testAppointment = await prisma.appointment.create({
      data: {
        patientId: testPatient.id,
        providerId: testProvider.id,
        appointmentDate: new Date(),
        reason: 'Test reason',
        status: 'SCHEDULED',
      },
    });
  });

  afterAll(async () => {
    // Clean up all test data
    await prisma.appointment.deleteMany({});
    await prisma.patient.deleteMany({});
    await prisma.provider.deleteMany({});
    await prisma.user.deleteMany({});
    await disconnectDatabase();
  });

  describe('POST /api/consultations/room', () => {
    it('should return 401 Unauthorized if no token is provided', async () => {
      // Ensure the mock does not attach a user
      (authenticateToken as jest.Mock).mockImplementation((req, res, next) => {
        // The real middleware would send 401, so we simulate that behavior for this test
        res.status(401).json({ success: false, error: 'Authentication required' });
      });

      const response = await request(app)
        .post('/api/consultations/room')
        .send({ appointmentId: testAppointment.id });

      expect(response.status).toBe(401);
    });

    it('should return 201 and create a consultation room if token is valid', async () => {
      // Mock a valid, authenticated user
      (authenticateToken as jest.Mock).mockImplementation((req, res, next) => {
        req.user = testUser;
        next();
      });

      const response = await request(app)
        .post('/api/consultations/room')
        .send({ appointmentId: testAppointment.id });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.consultation).toBeDefined();
      expect(response.body.data.roomUrl).toContain('daily.co');
      expect(response.body.data.consultation.appointmentId).toBe(testAppointment.id);

      // Verify the consultation was created in the database
      const consultation = await prisma.consultation.findUnique({
        where: { appointmentId: testAppointment.id },
      });
      expect(consultation).not.toBeNull();
      expect(consultation?.dailyRoomName).toBe(`appointment-${testAppointment.id}`);
    });
  });

  // ------------------- Additional Endpoints -------------------
  describe('Consultation Notes', () => {
    beforeAll(async () => {
      // Ensure a consultation exists for these tests
      await prisma.consultation.upsert({
        where: { appointmentId: testAppointment.id },
        update: {},
        create: {
          appointmentId: testAppointment.id,
          roomUrl: `https://mock-domain.daily.co/appointment-${testAppointment.id}`,
          dailyRoomId: `mock-room-${Date.now()}`,
          dailyRoomName: `appointment-${testAppointment.id}`,
          status: 'SCHEDULED',
        },
      });
    });

    it('should return empty notes initially', async () => {
      (authenticateToken as jest.Mock).mockImplementation((req, res, next) => {
        req.user = testUser; // patient role
        next();
      });

      const response = await request(app)
        .get(`/api/consultations/${testAppointment.id}/notes`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.notes).toBe('');
    });

    it('should forbid a patient from updating notes', async () => {
      (authenticateToken as jest.Mock).mockImplementation((req, res, next) => {
        req.user = testUser;
        next();
      });

      const response = await request(app)
        .patch(`/api/consultations/${testAppointment.id}/notes`)
        .send({ notes: 'Attempt' });

      expect(response.status).toBe(403);
    });

    it('should allow provider to update notes', async () => {
      (authenticateToken as jest.Mock).mockImplementation((req, res, next) => {
        req.user = testProvider.user;
        next();
      });

      const providerNotes = 'Consultation summary.';
      const response = await request(app)
        .patch(`/api/consultations/${testAppointment.id}/notes`)
        .send({ notes: providerNotes });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.notes).toBe(providerNotes);

      const dbConsult = await prisma.consultation.findUnique({ where: { appointmentId: testAppointment.id } });
      expect(dbConsult?.notes).toBe(providerNotes);
    });
  });

  describe('Consultation Recordings', () => {
    it('should return recordings list', async () => {
      const { dailyClient } = await import('@/config/daily');
      // Ensure getRoomRecordings is a mock function
      if (!(dailyClient.getRoomRecordings as jest.Mock).mock) {
        jest.spyOn(dailyClient, 'getRoomRecordings');
      }
      (dailyClient.getRoomRecordings as jest.Mock).mockResolvedValue([
        { id: 'rec1', room_name: `appointment-${testAppointment.id}`, url: 'https://example.com/rec1.mp4' },
      ]);

      (authenticateToken as jest.Mock).mockImplementation((req, res, next) => {
        req.user = testUser; // patient role permitted to view
        next();
      });

      const response = await request(app)
        .get(`/api/consultations/${testAppointment.id}/recordings`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data[0].id).toBe('rec1');
    });
  });
});
