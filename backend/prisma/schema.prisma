// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id                    String    @id @default(cuid())
  name                  String?
  email                 String?   @unique
  emailVerified         DateTime?
  image                 String?
  password              String?
  role                  String    @default("PATIENT")
  phone                 String?
  passwordResetToken    String?   @unique
  passwordResetExpires  DateTime?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  accounts      Account[]
  sessions      Session[]
  patient       Patient?
  provider      Provider?
  notifications Notification[]
  refreshTokens RefreshToken[]
}

model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Patient {
  id                String    @id @default(cuid())
  userId            String    @unique
  dateOfBirth       DateTime?
  address           String?
  phone             String?
  emergencyContact  Json?
  medicalHistory    Json?
  insurance         Json?
  preferences       Json?
  onboardingStatus  String    @default("INCOMPLETE") // INCOMPLETE, IN_PROGRESS, COMPLETED
  onboardingStep    Int       @default(1) // Current step in onboarding process
  consentGiven      Boolean   @default(false)
  consentDate       DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  appointments      Appointment[]
  medicalRecords    MedicalRecord[]
  payments          Payment[]
  allergies         Allergy[]
  medications       Medication[]
  conditions        Condition[]
  insuranceRecords  Insurance[]
  emergencyContacts EmergencyContact[]
}

model Provider {
  id                String    @id @default(cuid())
  userId            String    @unique
  specialization    String?
  bio               String?   @db.Text
  education         Json?
  experience        Json?
  licenseNumber     String?
  licenseState      String?
  licenseExpiry     DateTime?
  licenseVerified   Boolean   @default(false)
  approvalStatus    String    @default("PENDING") // PENDING, APPROVED, REJECTED
  approvedBy        String?   // Admin user ID who approved
  approvedAt        DateTime?
  rejectionReason   String?
  isVerified        Boolean   @default(false)
  isActive          Boolean   @default(true)
  consultationFee   Decimal?  @db.Decimal(10, 2)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  appointments      Appointment[]
  availability      Availability[]
  medicalRecords    MedicalRecord[]
}

model Appointment {
  id                String   @id @default(cuid())
  providerId        String
  patientId         String
  appointmentDate   DateTime
  duration          Int      @default(30) // Duration in minutes
  reason            String?
  status            String   @default("SCHEDULED") // SCHEDULED, CONFIRMED, CANCELLED, COMPLETED, NO_SHOW
  consultationType  String   @default("VIDEO") // VIDEO, AUDIO, IN_PERSON
  notes             String?  @db.Text
  isRecurring       Boolean  @default(false)
  recurrencePattern String?  // DAILY, WEEKLY, MONTHLY
  parentAppointmentId String? // For recurring appointments
  timezone          String?  // User's timezone when appointment was created
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  provider          Provider      @relation(fields: [providerId], references: [id], onDelete: Cascade)
  patient           Patient       @relation(fields: [patientId], references: [id], onDelete: Cascade)
  consultation      Consultation?
  payment           Payment?
  parentAppointment Appointment?  @relation("RecurringAppointments", fields: [parentAppointmentId], references: [id])
  childAppointments Appointment[] @relation("RecurringAppointments")
}

model Consultation {
  id            String   @id @default(cuid())
  appointmentId String   @unique
  roomUrl       String
  dailyRoomId   String?  @unique // Daily.co room ID
  dailyRoomName String?  @unique // Daily.co room name
  status        String   @default("SCHEDULED")
  videoEnabled  Boolean  @default(false) // Tracks if video was enabled in an audio call
  notes         String?
  startedAt     DateTime? // When the consultation actually started
  endedAt       DateTime? // When the consultation ended
  duration      Int?     // Duration in minutes
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  appointment   Appointment    @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  medicalRecord MedicalRecord?
}

model Notification {
  id        String   @id @default(cuid())
  title     String
  message   String   @db.Text
  type      String
  userId    String
  relatedId String?
  isRead    Boolean  @default(false)
  link      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Availability {
  id          String   @id @default(cuid())
  providerId  String
  dayOfWeek   String   // MONDAY, TUESDAY, etc.
  startTime   String   // HH:MM format
  endTime     String   // HH:MM format
  isAvailable Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  provider Provider @relation(fields: [providerId], references: [id], onDelete: Cascade)

  @@unique([providerId, dayOfWeek, startTime])
}

model MedicalRecord {
  id             String   @id @default(cuid())
  patientId      String
  providerId     String
  consultationId String?  @unique
  diagnosis      String?  @db.Text
  notes          String?  @db.Text
  prescriptions  Json?
  attachments    Json?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  patient      Patient       @relation(fields: [patientId], references: [id], onDelete: Cascade)
  provider     Provider      @relation(fields: [providerId], references: [id], onDelete: Cascade)
  consultation Consultation? @relation(fields: [consultationId], references: [id], onDelete: SetNull)
}

model Payment {
  id               String    @id @default(cuid())
  patientId        String
  appointmentId    String?   @unique
  amount           Decimal   @db.Decimal(10, 2)
  currency         String    @default("USD")
  status           String    @default("PENDING") // PENDING, COMPLETED, FAILED, REFUNDED
  paymentMethod    String    // STRIPE, PAYSTACK, PAYPAL, FLUTTERWAVE
  paymentIntentId  String?
  transactionId    String?
  metadata         Json?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  patient     Patient      @relation(fields: [patientId], references: [id], onDelete: Cascade)
  appointment Appointment? @relation(fields: [appointmentId], references: [id], onDelete: SetNull)
}

model Allergy {
  id          String   @id @default(cuid())
  patientId   String
  allergen    String   // Name of the allergen
  severity    String   // MILD, MODERATE, SEVERE
  reaction    String?  // Description of reaction
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  patient Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model Medication {
  id          String   @id @default(cuid())
  patientId   String
  name        String   // Medication name
  dosage      String?  // Dosage information
  frequency   String?  // How often taken
  startDate   DateTime?
  endDate     DateTime?
  prescriber  String?  // Who prescribed it
  notes       String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  patient Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model Condition {
  id          String   @id @default(cuid())
  patientId   String
  name        String   // Condition name
  diagnosedDate DateTime?
  status      String   @default("ACTIVE") // ACTIVE, RESOLVED, CHRONIC
  severity    String?  // MILD, MODERATE, SEVERE
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  patient Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model Insurance {
  id              String   @id @default(cuid())
  patientId       String
  provider        String   // Insurance provider name
  policyNumber    String
  groupNumber     String?
  subscriberName  String
  subscriberId    String?
  relationship    String   @default("SELF") // SELF, SPOUSE, CHILD, OTHER
  effectiveDate   DateTime?
  expirationDate  DateTime?
  copay           Decimal? @db.Decimal(10, 2)
  deductible      Decimal? @db.Decimal(10, 2)
  isPrimary       Boolean  @default(true)
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  patient Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
}

model EmergencyContact {
  id           String   @id @default(cuid())
  patientId    String
  name         String
  relationship String   // SPOUSE, PARENT, CHILD, SIBLING, FRIEND, OTHER
  phone        String
  email        String?
  address      String?
  isPrimary    Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  patient Patient @relation(fields: [patientId], references: [id], onDelete: Cascade)
}
