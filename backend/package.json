{"name": "fintan-virtual-care-backend", "version": "1.0.0", "description": "Backend API server for Dr. Fintan Virtual Care Hub", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "nodemon src/server.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node -r tsconfig-paths/register prisma/seed.ts"}, "keywords": ["telemedicine", "healthcare", "api", "nodejs", "express"], "author": "Dr. <PERSON>tan Virtual Care Hub", "license": "MIT", "type": "commonjs", "dependencies": {"@daily-co/daily-js": "^0.80.0", "@prisma/client": "^6.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "prisma": "^6.9.0", "stripe": "^18.2.1", "tsconfig-paths": "^4.2.0", "winston": "^3.17.0", "zod": "^3.25.65"}, "prisma": {"seed": "ts-node -r tsconfig-paths/register prisma/seed.ts"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/date-fns": "^2.5.3", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.0.3", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint": "^9.29.0", "jest": "^30.0.0", "nodemon": "^3.1.10", "prettier": "^3.5.3", "supertest": "^7.1.1", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}