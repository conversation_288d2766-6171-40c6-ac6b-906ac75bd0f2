{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "paths": {"@/*": ["./src/*"], "@prisma/client": ["./src/lib/stubs/prismaClientStub.ts"]}}, "include": ["src"], "exclude": ["src/__tests__/**/*", "src/tests/**/*", "src/examples/**/*", "src/lib/services/appointmentService.ts", "src/lib/services/calendarService.ts", "src/lib/services/**/*.jsdoc.ts", "src/lib/prisma.ts", "src/setupTests.ts", "src/lib/auth/authGuard.tsx"]}